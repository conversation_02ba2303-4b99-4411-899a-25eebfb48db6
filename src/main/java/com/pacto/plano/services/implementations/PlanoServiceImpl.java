package com.pacto.plano.services.implementations;

import com.pacto.config.utils.Calendario;
import com.pacto.config.utils.UteisValidacao;
import com.pacto.plano.adapters.CondicaoPagamentoAdapter;
import com.pacto.plano.adapters.horario.HorarioAdapter;
import com.pacto.plano.adapters.plano.PlanoAdapter;
import com.pacto.plano.adapters.plano.PlanoCategoriaAdapter;
import com.pacto.plano.adapters.plano.PlanoPacoteAdapter;
import com.pacto.plano.dao.interfaces.*;
import com.pacto.plano.dto.CondicaoPagamentoDTO;
import com.pacto.plano.dto.CondicaoPagamentoPlanoDuracaoDTO;
import com.pacto.plano.dto.ProdutoDTO;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.plano.dto.filtros.FiltroPlanoJSON;
import com.pacto.plano.dto.plano.*;
import com.pacto.plano.dto.plano.recorrencia.PlanoRecorrenciaParcelaDTO;
import com.pacto.plano.dto.usuario.UsuarioDTO;
import com.pacto.plano.entities.CondicaoPagamento;
import com.pacto.plano.entities.Log;
import com.pacto.plano.entities.horario.Horario;
import com.pacto.plano.entities.plano.*;
import com.pacto.plano.enumerators.ClasseProdutos;
import com.pacto.plano.enumerators.TipoHorario;
import com.pacto.plano.enumerators.TipoOperacao;
import com.pacto.plano.enumerators.TipoPlano;
import com.pacto.plano.enumerators.TipoProduto;
import com.pacto.plano.enumerators.TipoValor;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.security.interfaces.RequestService;
import com.pacto.plano.services.interfaces.*;
import com.pacto.plano.services.interfaces.planoempresa.PlanoEmpresaService;
import com.pacto.config.utils.HttpServico;
import com.pacto.config.utils.Uteis;
import org.hibernate.exception.ConstraintViolationException;
import org.hibernate.exception.DataException;
import org.hibernate.query.Query;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.PersistenceException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PlanoServiceImpl implements PlanoService {

    @Value("${zillyonweb.api.url}")
    private String urlZillyonWebApi;
    private final PlanoDao dao;
    private final PlanoJpaDao jpaDao;
    private final PlanoAdapter planoAdapter;
    private final RequestService requestService;
    private final LogService logService;
    private final ProdutoService produtoService;
    private final CondicaoPagamentoService condicaoPagamentoService;
    private final CondicaoPagamentoAdapter condicaoPagamentoAdapter;
    private final PlanoEmpresaService planoEmpresaService;
    private final PlanoDuracaoCreditoTreinoService planoDuracaoCreditoTreinoService;
    private final MessageSource messageSource;
    private final PlanoExcecaoService planoExcecaoService;
    private final PlanoDuracaoService planoDuracaoService;
    private final HorarioAdapter horarioAdapter;
    private final HorarioDao horarioDao;
    private final PlanoPacoteDao planoPacoteDao;
    private final PlanoPacoteAdapter planoPacoteAdapter;
    private final PlanoCategoriaDao planoCategoriaDao;
    private final CategoriaJpaDao categoriaJpaDao;
    private final PlanoCategoriaAdapter planoCategoriaAdapter;
    private final PlanoAvancadoService planoAvancadoService;
    private final PlanoProdutoSugeridoDao planoProdutoSugeridoDao;

    private final HttpServico httpServico;
    private final UsuarioService usuarioService;
    private final ModeloContratoService modeloContratoService;

    public PlanoServiceImpl(PlanoDao dao, PlanoJpaDao jpaDao, PlanoAdapter planoAdapter, RequestService requestService,
                            LogService logService, ProdutoService produtoService,
                            CondicaoPagamentoService condicaoPagamentoService, CondicaoPagamentoAdapter condicaoPagamentoAdapter, PlanoEmpresaService planoEmpresaService,
                            PlanoDuracaoCreditoTreinoService planoDuracaoCreditoTreinoService,
                            MessageSource messageSource, PlanoExcecaoService planoExcecaoService,
                            PlanoDuracaoService planoDuracaoService, HorarioDao horarioDao, HorarioAdapter horarioAdapter,
                            PlanoPacoteDao planoPacoteDao, PlanoPacoteAdapter planoPacoteAdapter,
                            HttpServico httpServico, PlanoCategoriaDao planoCategoriaDao, CategoriaJpaDao categoriaJpaDao,
                            PlanoCategoriaAdapter planoCategoriaAdapter, PlanoAvancadoService planoAvancadoService, PlanoProdutoSugeridoDao planoProdutoSugeridoDao,
                            ModeloContratoService modeloContratoService, UsuarioService usuarioService) {
        this.dao = dao;
        this.jpaDao = jpaDao;
        this.planoAdapter = planoAdapter;
        this.requestService = requestService;
        this.logService = logService;
        this.produtoService = produtoService;
        this.condicaoPagamentoService = condicaoPagamentoService;
        this.condicaoPagamentoAdapter = condicaoPagamentoAdapter;
        this.planoEmpresaService = planoEmpresaService;
        this.planoDuracaoCreditoTreinoService = planoDuracaoCreditoTreinoService;
        this.messageSource = messageSource;
        this.planoExcecaoService = planoExcecaoService;
        this.planoDuracaoService = planoDuracaoService;
        this.horarioDao = horarioDao;
        this.horarioAdapter = horarioAdapter;
        this.planoPacoteDao = planoPacoteDao;
        this.planoPacoteAdapter = planoPacoteAdapter;
        this.httpServico = httpServico;
        this.planoCategoriaDao = planoCategoriaDao;
        this.categoriaJpaDao = categoriaJpaDao;
        this.planoCategoriaAdapter = planoCategoriaAdapter;
        this.planoAvancadoService = planoAvancadoService;
        this.planoProdutoSugeridoDao = planoProdutoSugeridoDao;
        this.modeloContratoService = modeloContratoService;
        this.usuarioService = usuarioService;
    }

    public List<PlanoDTO> findAll(FiltroPlanoJSON filtros, PaginadorDTO paginadorDTO, Boolean app) throws ServiceException {
        try {
            List<Plano> planos = dao.findAllByEmpresa(filtros, paginadorDTO, requestService.getEmpresaId(), app);
            return planoAdapter.toDtos(planos);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    @Override
    public PlanoDTO plano(Integer id) throws ServiceException {
        try {
            dao.getCurrentSession().clear();
            Plano plano = dao.findById(id);
            return planoAdapter.toDto(plano);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public PlanoDTO clonarPlano(Integer id) throws ServiceException {
        try {
            Plano plano = dao.findById(id);
            PlanoDTO planoDTO = planoAdapter.toDto(plano);
            return planoDTO.clonar();
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    private void propagarAtributoReplicar(PlanoDTO planoDTO) {
        if (planoDTO.getReplicar()) {
            planoDTO.getDuracoes()
                    .forEach(planoDuracaoDTO -> {
                        planoDuracaoDTO.setReplicar(true);
                        planoDuracaoDTO.getCondicoesPagamento().forEach(planoCondicaoPagamentoDTO -> {
                            planoCondicaoPagamentoDTO.getCondicaoPagamento().setReplicar(planoDTO.getReplicar());
                        });
                    });
            planoDTO.getModalidades()
                    .forEach(planoModalidadeDTO -> planoModalidadeDTO.getModalidade().setReplicar(planoDTO.getReplicar()));
            planoDTO.getHorarios()
                    .forEach(planoHorarioDTO -> planoHorarioDTO.getHorario().setReplicar(planoDTO.getReplicar()));
            planoDTO.getExcecoes()
                    .forEach(planoExcecaoDTO -> planoExcecaoDTO.getHorario().setReplicar(planoDTO.getReplicar()));
            planoDTO.getPacotes()
                    .forEach(planoPacoteDTO -> {
                        planoPacoteDTO.getPacote().setReplicar(planoDTO.getReplicar());
                        planoPacoteDTO.getPacote().getModalidades()
                                .forEach(pacoteModalidadeDTO -> pacoteModalidadeDTO.getModalidade().setReplicar(planoDTO.getReplicar()));
                    });
            planoDTO.getProdutosSugeridos()
                    .forEach(planoProdutoSugeridoDTO -> planoProdutoSugeridoDTO.getProduto().setReplicar(planoDTO.getReplicar()));
            planoDTO.getExcecoes().forEach(planoExcecaoDTO -> {
                planoExcecaoDTO.getHorario().setReplicar(planoDTO.getReplicar());
                if (planoExcecaoDTO.getModalidade() != null) {
                    planoExcecaoDTO.getModalidade().setReplicar(planoDTO.getReplicar());
                }
                if (planoExcecaoDTO.getPacote() != null) {
                    planoExcecaoDTO.getPacote().setReplicar(planoDTO.getReplicar());
                    planoExcecaoDTO.getPacote().getModalidades()
                            .forEach(pacoteModalidadeDTO -> pacoteModalidadeDTO.getModalidade().setReplicar(planoDTO.getReplicar()));
                }
            });
        }
    }

    @Override
    @Transactional
    public PlanoDTO saveOrUpdate(final PlanoDTO planoDTO) throws ServiceException {
        try {
            planoDTO.setDescricao(planoDTO.getDescricao().toUpperCase().trim());
            if (planoDTO.getReplicar()) {
                planoDTO.setCodigo(dao.getCodigoByDescricaoAndEmpresa(planoDTO.getDescricao(), planoDTO.getEmpresa()));
                if (!UteisValidacao.emptyNumber(planoDTO.getCodigo())) {
                    planoDTO.setEmpresa(dao.findById(planoDTO.getCodigo()).getEmpresa());
                }
            }
            validations(planoDTO);
            validarProdutoAdesao(planoDTO);
            validarProdutoAnuidade(planoDTO);
            propagarAtributoReplicar(planoDTO);
            validarParcelamentoOperadora(planoDTO);
            PlanoAvancadoDTO planoAvancadoDTO = PlanoAvancadoDTO.getTo(planoDTO);

            if (planoDTO.getReplicar()) {
                for (PlanoHorarioDTO planoHorarioDTO : planoDTO.getHorarios()) {
                    this.criarHorarioPlano(planoHorarioDTO);
                }
            }
            Plano plano;
            Plano planoAnterior = new Plano();
            if (isNovoPlano(planoDTO)) {
                plano = planoAdapter.toEntity(planoDTO);
                if (!planoDTO.getReplicar()) {
                    plano.setEmpresa(requestService.getEmpresaId());
                }
                if (plano.getDuracoes() != null) {
                    plano.getDuracoes().forEach(
                            planoDuracao -> planoDuracao.setSituacao(true)
                    );
                }
                if (dao.existsByDescricao(plano)) {
                    throw new ServiceException(messageSource.getMessage("plano.duplicado", new Object[]{plano.getDescricao()}, new Locale(requestService.getLocale())));
                }
                if (plano.getRegimeRecorrencia()) {
                    this.criarDuracaoPlanoRecorrencia(plano);
                }
                plano = dao.save(plano);
                salvarPlanoAvancado(planoDTO, planoAvancadoDTO, plano);
                salvarCategoriaPlano(planoDTO, plano);

            } else {
                plano = dao.findById(planoDTO.getCodigo());
                planoAnterior = plano.clone();
                validarPlanoReplicadoRecorrente(planoDTO, plano);
                plano = planoAdapter.toEntity(planoDTO, plano);
                if (dao.existsByDescricao(plano)) {
                    throw new ServiceException(messageSource.getMessage("plano.duplicado", new Object[]{plano.getDescricao()}, new Locale(requestService.getLocale())));
                }
                if (plano.getRegimeRecorrencia()) {
                    this.criarDuracaoPlanoRecorrencia(plano);
                }
                adicionarPlanoProdutoSugeridoDosContratosPlanoAnterior(plano);
                plano = dao.update(plano);
                salvarPlanoAvancado(planoDTO, planoAvancadoDTO, plano);
                salvarCategoriaPlano(planoDTO, plano);
            }

            PlanoDTO dtoRetornar = planoAdapter.toDto(plano);

            if (planoDTO.getRegimeRecorrencia()) {
                jpaDao.alterarRenovacaoAutomaticaContratosRecorrencia(planoDTO.getPlanoRecorrencia().getRenovavelAutomaticamente(), planoDTO.getCodigo());
            } else {
                jpaDao.alterarRenovacaoAutomaticaContratos(planoDTO.getRenovavelAutomaticamente(), planoDTO.getCodigo());
            }
            if (planoDTO.getRegimeRecorrencia() && planoDTO.getPlanoRecorrencia().getAplicarDiasVencimentoContratosAtivos()) {
                jpaDao.alterarQtdDiasCancelamentoAutomaticoContratosRecorrencia(planoDTO.getPlanoRecorrencia().getQtdDiasAposVencimentoCancelamentoAutomatico(), planoDTO.getCodigo());
            }
            if (planoDTO.getRegimeRecorrencia() && planoDTO.getPlanoRecorrencia().getAplicarCancelamentoProporcionalContratosAtivos()) {
                jpaDao.alterarCancelamentoProporcionalContratosRecorrencia(planoDTO.getPlanoRecorrencia().getCancelamentoProporcional(), planoDTO.getPlanoRecorrencia().getQtdDiasCobrarProximaParcela(), planoDTO.getPlanoRecorrencia().getQtdDiasCobrarAnuidadeTotal(), planoDTO.getCodigo());
            }

            logService.incluirLogInclusaoAlteracao(plano, planoAnterior, "PLANO", "Plano");

            cleanApiCachePlan();

            if(planoDTO.getReplicar()) {
                try {
                    Log log = logService.registrarLogReplicacao("PLANO", "PLANO", "FRANQUEADORA",
                            requestService.getUsuarioAtual().getChave(), plano.getCodigo().toString(), null,
                            "REPLICADO EM " + Uteis.getDataComHora(Calendario.hoje()), requestService.getUsuarioAtual().getUsername() + " - FRANQUEADORA");
                } catch (Exception ignore) {
                }
            }

            return dtoRetornar;
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex.getCause() instanceof PersistenceException) {
                PersistenceException re = (PersistenceException) ex.getCause();
                if (ex.getCause() != null && ex.getCause().getCause() != null && ex.getCause().getCause().getClass() == ConstraintViolationException.class) {
                    String mensagem = ex.getCause().getCause().getCause().getMessage();
                    throw new ServiceException(messageSource.getMessage("registro.com.relacionamento", new Object[]{mensagem.substring(mensagem.lastIndexOf(" "))}, new Locale(requestService.getLocale())));
                }
                if (re.getCause() instanceof DataException) {
                    DataException de = (DataException) re.getCause();
                    throw new ServiceException(de.getSQLException());
                }
                throw new ServiceException(messageSource.getMessage("plano.erro.desconhecido", null, new Locale(requestService.getLocale())));
            }
            if (ex.getCause() instanceof ConstraintViolationException) {
                ConstraintViolationException constraintViolationException = ((ConstraintViolationException) ex.getCause());
                throw new ServiceException(constraintViolationException.getSQLException());
            }
            throw new ServiceException(ex);
        }
    }

    private void validarPlanoReplicadoRecorrente(PlanoDTO planoDTO, Plano plano) throws Exception{
        if (!planoDTO.getReplicar()) {
            return;
        }

        boolean regimeRecorrenciaDto = planoDTO.getRegimeRecorrencia() != null ? planoDTO.getRegimeRecorrencia() : false;
        boolean regimeRecorrencia = plano.getRegimeRecorrencia() != null ? plano.getRegimeRecorrencia() : false;
        if (regimeRecorrenciaDto != regimeRecorrencia && !regimeRecorrencia) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.plano-replicado-sem-recorrencia",
                    null,
                    new Locale(requestService.getLocale()))
            );
        } else if (regimeRecorrenciaDto != regimeRecorrencia && regimeRecorrencia) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.plano-replicado-com-recorrencia",
                    null,
                    new Locale(requestService.getLocale()))
            );
        }
    }


    private void salvarPlanoAvancado(PlanoDTO planoDTO, PlanoAvancadoDTO planoAvancadoDTO, Plano plano) {
        if (TipoPlano.PLANO_AVANCADO.name().equals(planoDTO.getTipoPlano())) {
            planoAvancadoService.salvarPlanoAvancado(planoAvancadoDTO, plano);
        }
    }

    private void salvarCategoriaPlano(PlanoDTO dto, Plano plano) throws Exception {
        if (TipoPlano.PLANO_AVANCADO.name().equals(dto.getTipoPlano()) || TipoPlano.PLANO_NORMAL.name().equals(dto.getTipoPlano())) {
            if (Boolean.FALSE.equals(dto.getRestringeVendaPorCategoria())) {
                planoCategoriaDao.deleteComParam(new String[]{"plano.codigo"}, new Object[]{plano.getCodigo()});
            } else {
                if (Objects.nonNull(dto.getCategorias()) && !dto.getCategorias().isEmpty()) {
                    planoAvancadoService.deletarRegistrosAntigos(plano.getCodigo(), planoCategoriaDao);
                    dto.getCategorias().forEach(c -> categoriaJpaDao.inserirPlanoCategoria(plano.getCodigo(), c.getCategoria().getCodigo()));
                }
            }
        }
    }

    private boolean isNovoPlano(PlanoDTO planoDTO) {
        return Uteis.intNullOrEmpty(planoDTO.getCodigo());
    }

    private void cleanApiCachePlan() {
        String chaveRequest;
        try {
            System.out.println("UrlApiZillyonWeb: " + urlZillyonWebApi);
            chaveRequest = requestService.getUsuarioAtual().getChave();
            String url = urlZillyonWebApi + "/prest/config/cache/plano/reload?key=" + chaveRequest;
            httpServico.doJson(url, "", HttpMethod.GET, new HttpHeaders());
        } catch (Exception ex) {
            System.out.println("Não foi possível atualizar a cache de planos na API-ZW");
        }
    }

    private void validarParcelamentoOperadora(PlanoDTO planoDTO) throws ServiceException {
        if (planoDTO.getParcelamentoOperadora() && Uteis.intNullOrEmpty(planoDTO.getMaximoVezesParcelar())) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.nenhum-maximo-vezes-parcelar",
                    null,
                    new Locale(requestService.getLocale()))
            );
        }

        if (planoDTO.getParcelamentoOperadora() && planoDTO.getPlanoRecorrencia() != null && planoDTO.getPlanoRecorrencia().getParcelasAnuidade() != null && planoDTO.getPlanoRecorrencia().getParcelasAnuidade().size() > 1) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.anuidade-parcelamento-operadora",
                    null,
                    new Locale(requestService.getLocale()))
            );
        }
    }

    private void validarProdutoAdesao(PlanoDTO planoDTO) throws ServiceException {
        ProdutoDTO produtoAdesaoDTO = produtoService.findProdutoPadraoAdesaoPlanoRecorrente();

        List<PlanoProdutoSugeridoDTO> produtosSugeridosAdesao = planoDTO.getProdutosSugeridos().stream()
                .filter(it -> (it.getProduto().getCodigo().equals(produtoAdesaoDTO.getCodigo())
                        || it.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ADESAO_PLANO_RECORRENCIA.getCodigo()))
                        && it.getAtivoPlano()
                ).collect(Collectors.toList());

        if (planoDTO.getPlanoRecorrencia() != null && planoDTO.getPlanoRecorrencia().getTaxaAdesao() != null && planoDTO.getPlanoRecorrencia().getTaxaAdesao() > 0.0 && !planoDTO.getReplicar()) {
            if (planoDTO.getCodigo() != null && !planoDTO.getPlanoPersonal() && planoDTO.getRegimeRecorrencia()) {
                if (produtosSugeridosAdesao.size() > 1) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.somente-um-produto-adesao-ativo",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }

                if (produtosSugeridosAdesao.isEmpty()) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.nenhum-produto-adesao-ativo",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }
            }
            PlanoProdutoSugeridoDTO produtoSugeridoAdesao = null;
            if (!produtosSugeridosAdesao.isEmpty()) {
                produtoSugeridoAdesao = produtosSugeridosAdesao.get(0);
            }

            if (produtoSugeridoAdesao == null) {
                PlanoProdutoSugeridoDTO produtoAdesao = new PlanoProdutoSugeridoDTO();
                produtoAdesao.setValorProduto(planoDTO.getPlanoRecorrencia().getTaxaAdesao());
                produtoAdesao.setObrigatorio(true);
                produtoAdesao.setProduto(produtoAdesaoDTO);
                produtoAdesao.setAtivoPlano(true);
                planoDTO.getProdutosSugeridos().add(produtoAdesao);
            } else {
                produtoSugeridoAdesao.setValorProduto(planoDTO.getPlanoRecorrencia().getTaxaAdesao());
            }
        }
    }

    private void validarProdutoAnuidade(PlanoDTO planoDTO) throws ServiceException {
        ProdutoDTO produtoAnuidadeDTO = produtoService.findProdutoPadraoAnuidadePlanoRecorrente();

        List<PlanoProdutoSugeridoDTO> produtosSugeridosAnuidade = planoDTO.getProdutosSugeridos().stream()
                .filter(it -> (it.getProduto().getCodigo().equals(produtoAnuidadeDTO.getCodigo())
                        || it.getProduto().getTipoProduto().equals(TipoProduto.TAXA_DE_ANUIDADE_PLANO_RECORRENCIA.getCodigo()))
                        && it.getAtivoPlano()
                ).collect(Collectors.toList());

        if (planoDTO.getPlanoRecorrencia() != null && !planoDTO.getReplicar()) {

            if (planoDTO.getCodigo() != null && !planoDTO.getPlanoPersonal() && planoDTO.getRegimeRecorrencia()) {
                if (produtosSugeridosAnuidade.size() > 1) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.somente-um-produto-anuidade-ativo",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }

                if (produtosSugeridosAnuidade.isEmpty()) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.nenhum-produto-anuidade-ativo",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }
            }

            PlanoProdutoSugeridoDTO produtoSugeridoAnuidade = null;
            if (!produtosSugeridosAnuidade.isEmpty()) {
                produtoSugeridoAnuidade = produtosSugeridosAnuidade.get(0);
            }

            if (produtoSugeridoAnuidade == null) {
                if (planoDTO.getPlanoRecorrencia().getValorAnuidade() != null) {
                    PlanoProdutoSugeridoDTO produtoAnuidade = new PlanoProdutoSugeridoDTO();
                    produtoAnuidade.setValorProduto(planoDTO.getPlanoRecorrencia().getValorAnuidade());
                    produtoAnuidade.setObrigatorio(true);
                    produtoAnuidade.setProduto(produtoAnuidadeDTO);
                    produtoAnuidade.setAtivoPlano(true);

                    planoDTO.getProdutosSugeridos().add(produtoAnuidade);
                }
            } else {
                produtoSugeridoAnuidade.setValorProduto(planoDTO.getPlanoRecorrencia().getValorAnuidade());
            }
        }
    }

    @Override
    public void delete(Integer id) throws ServiceException {
        try {
            Plano plano = dao.findById(id);
            planoEmpresaService.deleteAll(plano.getEmpresas());

            if (plano.getVendaCreditoTreino()) {
                for (PlanoDuracao duracao : plano.getDuracoes()) {
                    planoDuracaoCreditoTreinoService.deleteAll(duracao.getDuracoesCreditoTreino());
                }
            }
            removerCategoria(plano);
            dao.delete(id);
            logService.incluirLogExcluscao(plano, "PLANO", "Plano");
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    private void removerCategoria(Plano plano) throws Exception {
        planoCategoriaDao.deleteComParam(new String[]{"plano.codigo"}, new Object[]{plano.getCodigo()});
    }

    private void criarHorarioPlano(PlanoHorarioDTO planoHorarioDTO) throws Exception {
        Horario horario = horarioDao.findByDescription(planoHorarioDTO.getHorario().getDescricao());
        if (horario == null) {
            horario = horarioAdapter.toEntity(planoHorarioDTO.getHorario());
            horario.setCodigo(null);
            horarioDao.save(horario);
        }
        planoHorarioDTO.setHorario(horarioAdapter.toDto(horario));
    }

    private void criarDuracaoPlanoRecorrencia(Plano plano) throws ServiceException {
        CondicaoPagamento condicao = new CondicaoPagamento();
        if (plano.getPlanoRecorrencia() != null && plano.getPlanoRecorrencia().getDuracaoPlano() != null &&
                plano.getPlanoRecorrencia().getDuracaoPlano() == 0) {
            plano.getPlanoRecorrencia().setDuracaoPlano(1);
        }
        String plural = plano.getPlanoRecorrencia().getDuracaoPlano() == 1 ?
                messageSource.getMessage("condpag.vez", null, new Locale(requestService.getLocale())).toUpperCase() :
                messageSource.getMessage("condpag.vezes", null, new Locale(requestService.getLocale())).toUpperCase();

        condicao.setDescricao(messageSource.getMessage("condpag.descricao", new Object[]{plano.getPlanoRecorrencia().getDuracaoPlano(), plural}, new Locale(requestService.getLocale())));
        condicao.setIntervaloEntreParcela(30);
        condicao.setEntrada(true);
        condicao.setNrParcelas(plano.getPlanoRecorrencia().getDuracaoPlano());
        condicao.setGeradoAutomaticoPlanoRecorrente(true);
        List<CondicaoPagamento> condicoesPagamento = condicaoPagamentoService.findByDescricao(condicao.getDescricao());
        if (!condicoesPagamento.isEmpty())
            condicao = condicoesPagamento.get(0);
        else {
            CondicaoPagamentoDTO condicaoPagamentoDTO = condicaoPagamentoAdapter.toDto(condicao);
            condicaoPagamentoDTO = condicaoPagamentoService.saveOrUpdate(condicaoPagamentoDTO);
            condicao = condicaoPagamentoAdapter.toEntity(condicaoPagamentoDTO);
        }
        createPlanoCondicaoRecorrencia(plano, condicao);
    }

    private void createPlanoCondicaoRecorrencia(Plano plano, CondicaoPagamento condicaoPagamento) {
        PlanoDuracao planoDuracao = new PlanoDuracao();
        PlanoCondicaoPagamento planoCondicaoPagamento = new PlanoCondicaoPagamento();
        planoCondicaoPagamento.setCondicaoPagamento(condicaoPagamento);
        planoCondicaoPagamento.setQtdParcela(condicaoPagamento.getNrParcelas());

        if (plano.getCodigo() == null) {
            planoDuracao.setNrMaximoParcelasCondPagamento(plano.getPlanoRecorrencia().getDuracaoPlano());
            planoDuracao.setValorDesejadoMensal(plano.getPlanoRecorrencia().getValorMensal());
            planoDuracao.setValorDesejado(Uteis.arredondarForcando2CasasDecimais(plano.getPlanoRecorrencia().getValorMensal() * plano.getPlanoRecorrencia().getDuracaoPlano()));
            planoDuracao.setValorDesejadoParcela(plano.getPlanoRecorrencia().getValorMensal());
            planoDuracao.setNumeroMeses(plano.getPlanoRecorrencia().getDuracaoPlano());
            planoDuracao.setTipoValor(TipoValor.VALOR_EXATO.getCodigo());
            planoDuracao.setTipoOperacao(TipoOperacao.REDUCAO.getCodigo());
            if (!plano.getDuracoes().isEmpty()) {
                planoDuracao.setCarencia(plano.getDuracoes().stream().findFirst().orElse(new PlanoDuracao()).getCarencia());
            }
            planoDuracao.setSituacao(true);
            planoCondicaoPagamento.setPlanoDuracao(planoDuracao);
            planoDuracao.getCondicoesPagamento().add(planoCondicaoPagamento);
            Set<PlanoDuracao> duracoes = new HashSet<>();
            duracoes.add(planoDuracao);
            planoDuracao.setPlano(plano);
            plano.setDuracoes(duracoes);
        } else {
            plano.getDuracoes().forEach(
                    dur -> {
                        List<PlanoCondicaoPagamento> pc = dur.getCondicoesPagamento().stream()
                                .filter(cp -> cp.getCondicaoPagamento().getCodigo().equals(condicaoPagamento.getCodigo()))
                                .collect(Collectors.toList());
                        if (pc.isEmpty()) {
                            dur.setNrMaximoParcelasCondPagamento(plano.getPlanoRecorrencia().getDuracaoPlano());
                            dur.setValorDesejadoMensal(plano.getPlanoRecorrencia().getValorMensal());
                            dur.setValorDesejado(Uteis.arredondarForcando2CasasDecimais(plano.getPlanoRecorrencia().getValorMensal() * plano.getPlanoRecorrencia().getDuracaoPlano()));
                            dur.setValorDesejadoParcela(plano.getPlanoRecorrencia().getValorMensal());
                            dur.setNumeroMeses(plano.getPlanoRecorrencia().getDuracaoPlano());
                            dur.getCondicoesPagamento().forEach(
                                    planoCondicaoPagamento1 -> {
                                        planoCondicaoPagamento1.setCondicaoPagamento(condicaoPagamento);
                                        planoCondicaoPagamento1.setQtdParcela(condicaoPagamento.getNrParcelas());
                                    }
                            );
                        }
                    }
            );
        }

    }

    private void validations(PlanoDTO planoDTO) throws ServiceException {
        TipoPlano tipoPlano = TipoPlano.valueOf(planoDTO.getTipoPlano());

        if (planoDTO.getContratosEncerramDia() != null) {
            if (planoDTO.getDuracoes() != null) {
                for (PlanoDuracaoDTO planoDuracaoDTO : planoDTO.getDuracoes()) {
                    if (Calendario.menor(Calendario.somarMeses(Calendario.hoje(), planoDuracaoDTO.getNumeroMeses()), planoDTO.getContratosEncerramDia())) {
                        throw new ServiceException(messageSource.getMessage(
                                "validation.plano.duracoes.incompativel.encerramento.dia",
                                null,
                                new Locale(requestService.getLocale()))
                        );

                    }
                }
            }
            if (planoDTO.getPlanoRecorrencia() != null && planoDTO.getPlanoRecorrencia().getGerarParcelasValorDiferente()) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.recorrencia.parcelasDiferentes.incompativel.encerramento.dia",
                        null,
                        new Locale(requestService.getLocale()))
                );
            }
        }

        if (planoDTO.getDescricao().length() > 200) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.nome-cinquanta-caracteres",
                    null,
                    new Locale(requestService.getLocale()))
            );
        }

        if (Uteis.nullOrEmpty(planoDTO.getDescricao())) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.nome-vazio",
                    null,
                    new Locale(requestService.getLocale()))
            );
        }

        if (planoDTO.getProdutoContrato() == null || planoDTO.getProdutoContrato().getCodigo() == 0) {
            throw new ServiceException(messageSource.getMessage(
                    "validation.plano.produto-contrato-vazio",
                    null,
                    new Locale(requestService.getLocale()))
            );
        }

        if (isNonRecurringPlanDurationInvalid(planoDTO) || isRecurringPlanDurationInvalid(planoDTO)) {
            throw new ServiceException(messageSource.getMessage("validation.plano.duracao-numero-meses", null, new Locale(requestService.getLocale())));
        }

        if (planoDTO.getProdutosSugeridos() != null && !planoDTO.getProdutosSugeridos().isEmpty()) {
            boolean todosInativos = false;
            PlanoProdutoSugeridoDTO pps = null;
            for (PlanoProdutoSugeridoDTO ps : planoDTO.getProdutosSugeridos()) {
                for (PlanoProdutoSugeridoDTO ps2 : planoDTO.getProdutosSugeridos()) {
                    if (ps2.getProduto().getTipoProduto().equals(ps.getProduto().getTipoProduto())
                            && !ps2.getAtivoPlano()
                    ) {
                        todosInativos = true;
                        pps = ps;
                        break;
                    } else {
                        todosInativos = false;
                        pps = ps;
                    }
                }
                if (todosInativos) {
                    break;
                }
            }
            if (pps != null && ClasseProdutos.BASICO_PLANO.getTiposProdutos().contains(pps.getProduto().getTipoProduto())
                    && !pps.getAtivoPlano() && todosInativos
            ) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.produto-tipo-ativo",
                        new Object[]{Objects.requireNonNull(TipoProduto.getTipoProdutoCodigo(pps.getProduto().getTipoProduto())).getDescricao()},
                        new Locale(requestService.getLocale()))
                );
            }
        }

        if (TipoPlano.PLANO_RECORRENCIA.equals(tipoPlano) ||
                TipoPlano.PLANO_PERSONAL.equals(tipoPlano)) {
            if (planoDTO.getPlanoRecorrencia() == null) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.plano-recorrencia-vazio",
                        null,
                        new Locale(requestService.getLocale()))
                );
            }

            if (!planoDTO.getPlanoRecorrencia().getGerarParcelasValorDiferente()) {
                planoDTO.getPlanoRecorrencia().setGerarParcelasValorDiferenteRenovacao(false);
                if (!planoDTO.getPlanoRecorrencia().getParcelas().isEmpty()) {
                    planoDTO.getPlanoRecorrencia().setParcelas(null);
                }
            }

            if (TipoPlano.PLANO_RECORRENCIA.equals(tipoPlano) && planoDTO.getPlanoRecorrencia() != null && (planoDTO.getPlanoRecorrencia().getAnuidadeNaParcela() != null && !planoDTO.getPlanoRecorrencia().getAnuidadeNaParcela())) {
                if (planoDTO.getPlanoRecorrencia().getDiaAnuidade() <= 0) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.dia-anuidade-vazio",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }
                if (planoDTO.getPlanoRecorrencia().getMesAnuidade() <= 0) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.mes-anuidade-vazio",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }
            }

            if (planoDTO.getPlanoRecorrencia() != null && planoDTO.getPlanoRecorrencia().getParcelas().size() > 0) {
                Double valorMensal = planoDTO.getPlanoRecorrencia().getValorMensal();
                PlanoRecorrenciaParcelaDTO parcelaAtual = null;
                int parcelaDuplicada = 0;
                for (PlanoRecorrenciaParcelaDTO parcelaDTO : planoDTO.getPlanoRecorrencia().getParcelas()) {
                    if (parcelaAtual == null) {
                        parcelaAtual = parcelaDTO;
                    }
                    if (parcelaAtual.getNumero() == parcelaDTO.getNumero()) {
                        parcelaDuplicada++;
                    }
                    if (parcelaDuplicada > 1) {
                        throw new ServiceException(messageSource.getMessage(
                                "validation.plano.parcela-duplicada",
                                new Object[]{parcelaDTO.getNumero()},
                                new Locale(requestService.getLocale()))
                        );
                    }
                    if (parcelaDTO.getValor() > valorMensal) {
                        throw new ServiceException(messageSource.getMessage(
                                "validation.plano.valor-parcela-superior-valor-mensal",
                                new Object[]{parcelaDTO.getNumero()},
                                new Locale(requestService.getLocale()))
                        );
                    } else if (parcelaDTO.getValor().equals(valorMensal)) {
                        throw new ServiceException(messageSource.getMessage(
                                "validation.plano.valor-parcela-igual-valor-mensal",
                                new Object[]{parcelaDTO.getNumero()},
                                new Locale(requestService.getLocale()))
                        );
                    }
                }
            }
        }

        if (TipoPlano.PLANO_NORMAL.equals(tipoPlano)) {
            if (planoDTO.getExcecoes() != null && !planoDTO.getExcecoes().isEmpty()) {
                for (PlanoExcecaoDTO excecaoDTO : planoDTO.getExcecoes()) {
                    planoExcecaoService.validations(excecaoDTO);
                }

                boolean existeModalidadeNaoZerada = false;
                boolean semModalidades = true;
                if (null != planoDTO.getBolsa() && !planoDTO.getPlanoPersonal() && !planoDTO.getBolsa()) {
                    for (PlanoExcecaoDTO excecaoDTO : planoDTO.getExcecoes()) {
                        if (excecaoDTO.getModalidade() != null) {
                            if (excecaoDTO.getModalidade().getValorMensal() > 0) {
                                existeModalidadeNaoZerada = true;
                                break;
                            }
                            semModalidades = false;
                        }
                    }
                    if (!semModalidades) {
                        if (!existeModalidadeNaoZerada) {
                            throw new ServiceException(messageSource.getMessage(
                                    "validation.plano.modalidade-valor-zerado-nao-plano-bolsa",
                                    null,
                                    new Locale(requestService.getLocale())),
                                    "dadosModalidade"
                            );
                        }
                    }
                }
            } else {
                if (!planoDTO.getReplicar()) {
                    throw new ServiceException(messageSource.getMessage(
                        "validation.plano.excecoes-nao-informadas",
                            null,
                            new Locale(requestService.getLocale())));
                }
            }

            if (planoDTO.getDuracoes() == null || planoDTO.getDuracoes().isEmpty()) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.duracoes-nao-informadas",
                        null,
                        new Locale(requestService.getLocale())));
            } else {
                for (PlanoDuracaoDTO duracaoDTO : planoDTO.getDuracoes()) {
                    planoDuracaoService.validations(duracaoDTO, tipoPlano);
                }
            }
        }

        if (TipoPlano.PLANO_CREDITO.equals(tipoPlano)) {
            if (planoDTO.getModalidades() == null || planoDTO.getModalidades().isEmpty()) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.modalidade-nao-informada",
                        null,
                        new Locale(requestService.getLocale()))
                );
            }

            boolean existeModalidadeNaoZerada = false;
            if (null != planoDTO.getBolsa() && !planoDTO.getPlanoPersonal() && !planoDTO.getBolsa()) {
                for (PlanoModalidadeDTO planoModalidadeDTO : planoDTO.getModalidades()) {
                    if (planoModalidadeDTO.getModalidade().getValorMensal() > 0) {
                        existeModalidadeNaoZerada = true;
                        break;
                    }
                }
                if (!existeModalidadeNaoZerada) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.modalidade-valor-zerado-nao-plano-bolsa",
                            null,
                            new Locale(requestService.getLocale())),
                            "dadosModalidade"
                    );
                }
            }
            if (planoDTO.getCreditoSessao()) {
                List<PlanoModalidadeDTO> modalidadeDTOS = planoDTO.getModalidades().stream().filter(planoModalidadeDTO ->
                        !planoModalidadeDTO.getModalidade().getUtilizaTurma()).collect(Collectors.toList()
                );
                if (!modalidadeDTOS.isEmpty()) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.credito-sessao.somente-modalidades-turma",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }
            }
            if (planoDTO.getHorarios() == null || planoDTO.getHorarios().isEmpty()) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.horarios-nao-informado",
                        null,
                        new Locale(requestService.getLocale()))
                );
            }
            if (planoDTO.getDuracoes() == null || planoDTO.getDuracoes().isEmpty()) {
                throw new ServiceException(messageSource.getMessage(
                        "validation.plano.duracoes-nao-informadas",
                        null,
                        new Locale(requestService.getLocale()))
                );
            }
            if (!planoDTO.getDuracoes().isEmpty()) {
                int countDuracoesDuplicadas = 0;
                for (PlanoDuracaoDTO planoDuracaoDTO : planoDTO.getDuracoes()) {
                    if (planoDTO.getDuracoes().stream().filter(planoDuracaoDTO1 -> planoDuracaoDTO.getNumeroMeses() == planoDuracaoDTO1.getNumeroMeses()).collect(Collectors.toList()).size() > 1) {
                        throw new ServiceException(messageSource.getMessage(
                                "validation.plano.duracao-duplicada",
                                new Object[]{planoDuracaoDTO.getNumeroMeses()},
                                new Locale(requestService.getLocale()))
                        );
                    }
                    if (planoDuracaoDTO.getNumeroMeses() == 1 && planoDuracaoDTO.getDuracoesCreditoTreino().isEmpty()) {
                        throw new ServiceException(messageSource.getMessage("validation.plano.duracao-um-mes-sem-config-credito-treino", null, new Locale(requestService.getLocale())));
                    }
                    List<PlanoDuracaoCreditoTreinoDTO> planoDuracaoCreditoTreinoDTOS = planoDuracaoDTO.getDuracoesCreditoTreino().stream()
                            .filter(
                                    planoDuracaoCreditoTreinoDTO -> planoDuracaoCreditoTreinoDTO.getTipoHorarioCreditoTreino() != null && (planoDuracaoCreditoTreinoDTO.getTipoHorarioCreditoTreino().equals(TipoHorario.LIVRE.getTipoHorarioCodigo()) ||
                                            planoDuracaoCreditoTreinoDTO.getTipoHorarioCreditoTreino().equals(TipoHorario.LIVRE_OBRIGATORIO_MARCAR_AULA.getTipoHorarioCodigo()))
                            ).collect(Collectors.toList());
                    List<PlanoHorarioDTO> planoHorarioDTOS = planoDTO.getHorarios().stream().filter(planoHorarioDTO -> planoHorarioDTO.getHorario().getLivre()).collect(Collectors.toList());

                    if (planoDTO.getCreditoSessao()) {
                        if (!planoHorarioDTOS.isEmpty()) {
                            throw new ServiceException(messageSource.getMessage("validation.plano.credito-sessao-somente-horario-turma", null, new Locale(requestService.getLocale())));
                        }
                    }
                    if (!planoDuracaoCreditoTreinoDTOS.isEmpty() && planoHorarioDTOS.isEmpty()) {
                        throw new ServiceException(messageSource.getMessage("validation.plano.credito-horario-livre-sem-horario-livre", null, new Locale(requestService.getLocale())));
                    }
                    planoDuracaoCreditoTreinoDTOS = planoDuracaoDTO.getDuracoesCreditoTreino().stream()
                            .filter(
                                    planoDuracaoCreditoTreinoDTO -> planoDuracaoCreditoTreinoDTO.getTipoHorarioCreditoTreino() != null && planoDuracaoCreditoTreinoDTO.getTipoHorarioCreditoTreino().equals(TipoHorario.HORARIO_TURMA.getTipoHorarioCodigo())
                            ).collect(Collectors.toList());
                    planoHorarioDTOS = planoDTO.getHorarios().stream().filter(planoHorarioDTO -> !planoHorarioDTO.getHorario().getLivre()).collect(Collectors.toList());
                    if (!planoDuracaoCreditoTreinoDTOS.isEmpty() && planoHorarioDTOS.isEmpty()) {
                        throw new ServiceException(messageSource.getMessage("validation.plano.credito-horario-turma-sem-horario-turma", null, new Locale(requestService.getLocale())));
                    }
                    if (!planoDuracaoDTO.getDuracoesCreditoTreino().isEmpty()) {
                        for (PlanoDuracaoCreditoTreinoDTO creditoTreino: planoDuracaoDTO.getDuracoesCreditoTreino()){
                            if (planoDTO.getCreditoTreinoNaoCumulativo() && (creditoTreino.getQuantidadeCreditoMensal() == null || creditoTreino.getQuantidadeCreditoMensal() <= 0)) {
                                throw new ServiceException(messageSource.getMessage("validation.plano.credito-quantidade-credito-mensal", null, new Locale(requestService.getLocale())));
                            }
                        }
                    }
                }
            }
        }

        if (TipoPlano.PLANO_RECORRENCIA.equals(tipoPlano)) {
            if (planoDTO.getModalidades() == null || planoDTO.getModalidades().isEmpty()) {
                throw new ServiceException(messageSource.getMessage("validation.plano.sem-modalidade", null, new Locale(requestService.getLocale())));
            }

            if (planoDTO.getHorarios() == null || planoDTO.getHorarios().isEmpty()) {
                throw new ServiceException(messageSource.getMessage("validation.plano.sem-horario", null, new Locale(requestService.getLocale())));
            }

            boolean existeModalidadeNaoZerada = false;
            if (null != planoDTO.getBolsa() && !planoDTO.getPlanoPersonal() && !planoDTO.getBolsa()) {
                for (PlanoModalidadeDTO planoModalidadeDTO : planoDTO.getModalidades()) {
                    if (planoModalidadeDTO.getModalidade().getValorMensal() > 0) {
                        existeModalidadeNaoZerada = true;
                        break;
                    }
                }
                if (!existeModalidadeNaoZerada) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.modalidade-valor-zerado-nao-plano-bolsa", null, new Locale(requestService.getLocale())));
                }
            }
        }

        if (planoDTO.getSite() || planoDTO.getApresentarPactoFlow()) {

            if(planoDTO.getObservacaoSite() != null && !planoDTO.getObservacaoSite().isEmpty()) {
                if (planoDTO.getObservacaoSite().length() > 500) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.observacao-site", null, new Locale(requestService.getLocale())));
                }
            }

            if(planoDTO.getObservacaoSite() != null && !planoDTO.getObservacaoSite().isEmpty()) {
                if (planoDTO.getObservacaoSite().length() > 500) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.observacao-site", null, new Locale(requestService.getLocale())));
                }
            }

            if (planoDTO.getTipoPlano().equals(TipoPlano.PLANO_NORMAL.name()) || planoDTO.getTipoPlano().equals(TipoPlano.PLANO_AVANCADO.name())) {
                if (!planoDTO.getPermitirTurmasVendasOnline() && planoDTO.getExcecoes().size() > 1) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.duracao-plano-site", null, new Locale(requestService.getLocale())));
                } else if (planoDTO.getPermitirTurmasVendasOnline() && planoDTO.getExcecoes().size() > 1) {
                    int duracaoPadrao = planoDTO.getExcecoes().get(0).getDuracao();
                    for (PlanoExcecaoDTO excecaoDTO : planoDTO.getExcecoes()) {
                        if (excecaoDTO.getDuracao() != duracaoPadrao) {
                            throw new ServiceException(messageSource.getMessage("validation.plano.duracao-plano-site", null, new Locale(requestService.getLocale())));
                        }
                    }
                }
                if (planoDTO.getExcecoes() != null && !planoDTO.getExcecoes().isEmpty()) {
                    if (planoDTO.getExcecoes().get(0).getModalidade() == null) {
                        throw new ServiceException(messageSource.getMessage("validation.plano.site-sem-modalidade", null, new Locale(requestService.getLocale())));
                    }
                }

                List<PlanoExcecaoDTO> modalidadeComTurma = planoDTO.getExcecoes().stream().filter(planoExcecaoDTO ->
                        planoExcecaoDTO.getModalidade().getUtilizaTurma()).collect(Collectors.toList()
                );
                if (!modalidadeComTurma.isEmpty() && !planoDTO.getPermitirTurmasVendasOnline()) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.site.somente-modalidades-turma",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }

                if (planoDTO.getPermitirTurmasVendasOnline() && planoDTO.getExcecoes().size() > 1) {
                    String descHorario = planoDTO.getExcecoes().get(0).getHorario().getDescricao();
                    for (PlanoExcecaoDTO excecaoDTO : planoDTO.getExcecoes()) {
                        if (!excecaoDTO.getHorario().getDescricao().equals(descHorario)) {
                            throw new ServiceException(messageSource.getMessage("validation.plano.horario-plano-turma-site", null, new Locale(requestService.getLocale())));
                        }
                    }
                }
            }

            if (planoDTO.getDuracoes() != null && planoDTO.getDuracoes().size() > 1) {
                throw new ServiceException(messageSource.getMessage("validation.plano.duracao-plano-site", null, new Locale(requestService.getLocale())));
            }

            if (!UteisValidacao.emptyList(planoDTO.getDuracoes()) && (planoDTO.getDuracoes().get(0).getCondicoesPagamento() != null && planoDTO.getDuracoes().get(0).getCondicoesPagamento().size() > 1)) {
                throw new ServiceException(messageSource.getMessage("validation.plano.site-uma-cond-pagamento", null, new Locale(requestService.getLocale())));
            }

            if (planoDTO.getHorarios() != null && planoDTO.getHorarios().size() > 1) {
                if (!planoDTO.getPermitirTurmasVendasOnline() && planoDTO.getSite()) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.site-um-horario", null, new Locale(requestService.getLocale())));
                } else if (planoDTO.getApresentarPactoFlow()) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.pactoflow-um-horario", null, new Locale(requestService.getLocale())));
                } else {
                    int horarioPadrao = planoDTO.getExcecoes().get(0).getHorario().getCodigo();
                    for (PlanoExcecaoDTO excecaoDTO : planoDTO.getExcecoes()) {
                        if (excecaoDTO.getHorario().getCodigo() != horarioPadrao) {
                            throw new ServiceException(messageSource.getMessage("validation.plano.duracao-plano-site", null, new Locale(requestService.getLocale())));
                        }
                    }
                }
            }

            if (!planoDTO.getTipoPlano().equals(TipoPlano.PLANO_NORMAL.name()) && !planoDTO.getTipoPlano().equals(TipoPlano.PLANO_AVANCADO.name())) {
                List<PlanoModalidadeDTO> modalidadeDTOS = planoDTO.getModalidades().stream().filter(planoModalidadeDTO ->
                        planoModalidadeDTO.getModalidade().getUtilizaTurma()).collect(Collectors.toList()
                );
                if (!modalidadeDTOS.isEmpty() && !planoDTO.getPermitirTurmasVendasOnline()) {
                    throw new ServiceException(messageSource.getMessage(
                            "validation.plano.site.somente-modalidades-turma",
                            null,
                            new Locale(requestService.getLocale()))
                    );
                }
            }
        }

        if (planoDTO.getModeloContrato() == null || planoDTO.getModeloContrato().getCodigo() == 0) {
            throw new ServiceException(messageSource.getMessage("validation.plano.modelo-contrato-nao-informado", null, new Locale(requestService.getLocale())));
        }

        if (planoDTO.getProdutoTaxaCancelamento() == null || planoDTO.getProdutoTaxaCancelamento().getCodigo() == 0) {
            throw new ServiceException(messageSource.getMessage("validation.plano.produto-taxa-cancelamento-nao-informado", null, new Locale(requestService.getLocale())));
        }


        if (TipoPlano.PLANO_RECORRENCIA.equals(tipoPlano)) {
            if (planoDTO.getPlanoRecorrencia().getValorMensal() == null || (planoDTO.getPlanoRecorrencia().getValorMensal() != null && planoDTO.getPlanoRecorrencia().getValorMensal() <= 0.0)) {
                throw new ServiceException(messageSource.getMessage("validation.plano.valor-mensal-zerado", null, new Locale(requestService.getLocale())));
            }
        }

        if (TipoPlano.PLANO_CREDITO.equals(tipoPlano)) {
            if (planoDTO.getGerarValorCreditoExtra()) {
                if (planoDTO.getValorCreditoExtra() == null || (planoDTO.getValorCreditoExtra() != null && planoDTO.getValorCreditoExtra() <= 0.0)) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.valor-gerar-credito-extra-nao-informado", null, new Locale(requestService.getLocale())));
                }
                if (planoDTO.getProdutoCreditoExtra() == null || planoDTO.getProdutoCreditoExtra().getCodigo() == 0) {
                    throw new ServiceException(messageSource.getMessage("validation.plano.produto-gerar-credito-extra-nao-informado", null, new Locale(requestService.getLocale())));
                }
            }
        }

    }

    private boolean isRecurringPlanDurationInvalid (PlanoDTO planoDTO) {
        return (planoDTO.getPlanoRecorrencia()) != null && (planoDTO.getRegimeRecorrencia() && planoDTO.getPlanoRecorrencia().getDuracaoPlano() < 1);
    }

    private boolean isNonRecurringPlanDurationInvalid(PlanoDTO planoDTO) {
        return (!planoDTO.getRegimeRecorrencia())
                && (planoDTO.getDuracoes() != null && !planoDTO.getDuracoes().isEmpty() && planoDTO.getDuracoes().stream().anyMatch(duracaoDTO -> duracaoDTO.getNumeroMeses() < 1));
    }

    public List<PlanoDTO> findAllCodName(FiltroPlanoJSON filtros, PaginadorDTO paginadorDTO) throws ServiceException {
        try {
            List<Plano> planos = dao.findAllCodName(filtros, paginadorDTO, requestService.getEmpresaId());
            List<PlanoDTO> planoDTOS = new ArrayList<>();
            planos.forEach(p -> {
                PlanoDTO planoDTO = new PlanoDTO();
                planoDTO.setCodigo(p.getCodigo());
                planoDTO.setDescricao(p.getDescricao());
                planoDTOS.add(planoDTO);
            });
            return planoDTOS;
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException(ex);
        }
    }

    public List<PlanoDTO> findByIdCondicaoPagamento(FiltroPlanoJSON filtroPlanoJSON, Integer idCondicaoPagamento) throws ServiceException {
        try {
            List<Plano> planos = dao.findByIdCondicaoPagamento(filtroPlanoJSON, idCondicaoPagamento, requestService.getEmpresaId());
            return planoAdapter.toDtos(planos);

        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<PlanoDTO> saveOrUpdatePlanos(List<PlanoDTO> planos) {
        for (PlanoDTO planoDTO : planos) {
            try {
                saveOrUpdate(planoDTO);
            } catch (ServiceException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public List<PlanoDTO> saveOrUpdatePlanosCondicaoPagamento(List<CondicaoPagamentoPlanoDuracaoDTO> condicaoPagamentoPlanosDuracoes, Integer idCondicaoPagamento) throws ServiceException {
        FiltroPlanoJSON filtro = new FiltroPlanoJSON(new JSONObject());
        List<Plano> planos = dao.findByIdCondicaoPagamento(filtro, idCondicaoPagamento, requestService.getEmpresaId());
        List<PlanoDTO> planosDTOs = planoAdapter.toDtos(planos);
        for (CondicaoPagamentoPlanoDuracaoDTO c : condicaoPagamentoPlanosDuracoes) {
            try {
                PlanoDTO planoDTO = planoAdapter.toDto(dao.findById(c.getPlano()));
                boolean estaNaLista = false;
                for (PlanoDTO p : planosDTOs) {
                    if (p.getCodigo() == planoDTO.getCodigo()) {
                        estaNaLista = true;
                        break;
                    }
                }
                if (!estaNaLista) {
                    planosDTOs.add(planoDTO);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException(e);
            }
        }
        // Percorrer a lista de planos vinculados a condicao de pagamento, e remover ou adicionar o PlanoCondicaoPagamento das duracoes que nao estao na condicaoPagamentoPlanosDuracoes
        for (PlanoDTO p : planosDTOs) {
            List<PlanoDuracaoDTO> duracoes = atualizarPlanoCondicaoPagamento(p, condicaoPagamentoPlanosDuracoes, idCondicaoPagamento);
            if (duracoes.size() > 0) {
                for (PlanoDuracaoDTO planoDuracaoDTO : duracoes) {
                    planoDuracaoService.updateByCondicaoPagamento(planoDuracaoDTO, p.getCodigo());
                }
            }
        }
        return planosDTOs;
    }

    private List<PlanoDuracaoDTO> atualizarPlanoCondicaoPagamento(PlanoDTO planoDTO, List<CondicaoPagamentoPlanoDuracaoDTO> condicaoPagamentoPlanosDuracoes, Integer idCondicaoPagamento) {
        Integer indexPlanoCondicaoPagamentoNaDuracao = -1;
        List<PlanoDuracaoDTO> duracoes = new ArrayList<>();
        // Percorrer as duracoes e condicoes de pagamento do plano e remover ou adicionar o plano condicao pagamento de acordo com a lista de planos e duracoes da condicao de pagamento
        for (int a = 0; a < planoDTO.getDuracoes().size(); a++) {
            for (int b = 0; b < planoDTO.getDuracoes().get(a).getCondicoesPagamento().size(); b++) {
                if (planoDTO.getDuracoes().get(a).getCondicoesPagamento().get(b)
                        .getCondicaoPagamento().getCodigo().equals(idCondicaoPagamento)) {
                    // Verificar se a condicao de pagamento dessa duracao, esta na lista de duracoes da condicao de pagamento
                    Integer indexPlanoDuracaoNaCondicaoPagamento = -1;
                    for (int c = 0; c < condicaoPagamentoPlanosDuracoes.size(); c++) {
                        if (condicaoPagamentoPlanosDuracoes.get(c).getPlanoDuracao().equals(planoDTO.getDuracoes().get(a).getCodigo())) {
                            indexPlanoDuracaoNaCondicaoPagamento = c;
                            break;
                        }
                    }
                    // Se a duracao nao estiver nao estiver na lista, entao remover o plano condicao pagamento
                    if (indexPlanoDuracaoNaCondicaoPagamento == -1) {
                        planoDTO.getDuracoes().get(a).getCondicoesPagamento().remove(b);
                        duracoes.add(planoDTO.getDuracoes().get(a));
                    }
                    indexPlanoCondicaoPagamentoNaDuracao = b;
                }
            }
            // Adicionar o plano condicao pagamento no plano duracao que nao tem essa condicao de pagamento
            for (int b = 0; b < condicaoPagamentoPlanosDuracoes.size(); b++) {
                if (condicaoPagamentoPlanosDuracoes.get(b).getPlanoDuracao().equals(planoDTO.getDuracoes().get(a).getCodigo())) {
                    if (indexPlanoCondicaoPagamentoNaDuracao == -1) {
                        PlanoCondicaoPagamentoDTO planoCondicaoPagamentoDTO = new PlanoCondicaoPagamentoDTO();
                        planoCondicaoPagamentoDTO.setCondicaoPagamento(condicaoPagamentoPlanosDuracoes.get(b).getCondicaoPagamento());
                        planoCondicaoPagamentoDTO.setQtdParcela(planoCondicaoPagamentoDTO.getQtdParcela());
                        planoDTO.getDuracoes().get(a).getCondicoesPagamento().add(planoCondicaoPagamentoDTO);
                        duracoes.add(planoDTO.getDuracoes().get(a));
                    }
                }
            }

        }
        return duracoes;
    }

    public Integer consultarQuantidadeContratosAtivosPorPlano(Integer codigoPlano) {
        return dao.countActiveRecurrentContractsByPlano(codigoPlano);
    }

    @Override
    public List<PlanoPacoteDTO> consultarPacotes(Integer plano) {
        List<PlanoPacote> planoPacotes = this.planoPacoteDao.consultarPorPlano(plano);
        return this.planoPacoteAdapter.toDtos(planoPacotes);
    }

    @Override
    public List<PlanoCategoriaDTO> consultarCategoria(Integer plano) {
        List<PlanoCategoria> planoCategorias = this.planoCategoriaDao.consultarPorPlano(plano);
        return this.planoCategoriaAdapter.toDtos(planoCategorias);
    }

    @Override
    public PlanoDTO atualizarPlano(Integer codigo) throws ServiceException {
        try {
            Plano plano = dao.findById(codigo);
            plano.setPlanoAvancado(true);
            plano = dao.update(plano);
            return planoAdapter.toDto(plano);
        }catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public boolean existeContratoDuracao(Integer codigoPlano, Integer duracao) {
        try {
            String sql = "SELECT CASE WHEN EXISTS ( " +
                    "    SELECT 1 " +
                    "    FROM contratoduracao CD " +
                    "    INNER JOIN contrato C ON C.codigo = CD.contrato " +
                    "    WHERE C.plano = :codigoPlano " +
                    "    AND CD.numeromeses = :duracao " +
                    ") THEN 1 ELSE 0 END";

            Query query = dao.getCurrentSession().createSQLQuery(sql);
            query.setParameter("codigoPlano", codigoPlano);
            query.setParameter("duracao", duracao);
            Object result = query.getSingleResult();
            return result != null && Integer.parseInt(result.toString()) == 1;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public PlanoContratoDTO aplicarTextoContratoJaLancados(PlanoContratoDTO planoContratoDTO) throws Exception {
        if (TipoPlano.PLANO_PERSONAL.name().equals(planoContratoDTO.getTipoPlano())) {
            this.dao.atualizarPlanoPersonalTextoPadrao(planoContratoDTO.getCodigo(), planoContratoDTO.getModeloDeContrato());
        }
        else {
            boolean isPermiteImpressaoMutavel = dao.isPermiteImpressaoContratoMutavel();
            this.jpaDao.atualizarTextoContratosExistentes(planoContratoDTO.getCodigo(), planoContratoDTO.getModeloDeContrato());
            if (!isPermiteImpressaoMutavel) {
                List<Integer> contratosIds = this.dao.consultarContratosExistentesDoPlano(planoContratoDTO.getCodigo());
                UsuarioDTO usuarioDTO = usuarioService.usuario(requestService.getUsuarioAtual().getCodZw());
                contratosIds.forEach(id -> {
                    this.dao.atualizarTextoPadraoPlano(id, usuarioDTO);
                });
            }
        }
        return planoContratoDTO;
    }

    private void adicionarPlanoProdutoSugeridoDosContratosPlanoAnterior(Plano plano) {
        StringBuilder codigosProdutos = new StringBuilder("0");
        if (plano.getProdutosSugeridos() == null) {
            plano.setProdutosSugeridos(new HashSet<>());
        }

        plano.getProdutosSugeridos().forEach((produto) -> {
            if (produto.getProduto() != null && produto.getProduto().getCodigo() != null) {
                codigosProdutos.append(","+produto.getProduto().getCodigo());
            }
        });

        List<PlanoProdutoSugerido> lista = planoProdutoSugeridoDao.getPlanoProdutoSugeridoDosContratosPlanoAnterior(plano.getCodigo(), codigosProdutos.toString());
        if (lista == null) {
            return;
        }

        lista.forEach((produto) -> {
            produto.setAtivoPlano(false);
            produto.setObrigatorio(false);
            plano.getProdutosSugeridos().add(produto);
        });
    }

}
